const db = require("../db/index");
const log = require('./log')
const jwt = require("jsonwebtoken");
const config = require("../config");
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const {
  IMAGE_TYPES,
  IMAGE_PATHS,
  createUploader,
  handleFileUpload,
  ensureDirectoryExists
} = require('../utils/imageManager');

// 注册处理函数
exports.userRegister = (req, res) => {
    try {
        // 验证必要的字段
        const { username, password, phone, name, gender, birthday, idCard } = req.body;

        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400);
        }

        let role = "user";

        // 检查用户名是否已存在
        const sql = "select username from users where username=?";
        db.query(sql, username, (err, results) => {
            if (err) return res.output(err);
            if (results.length === 1) return res.output(new Error("用户名被占用"), 400);

            // 插入用户信息
            const sqlStr = "insert into users set ?";
            db.query(sqlStr, { username, password, phone, name, gender, birthday, idCard, role }, (err, results) => {
                if (err) return res.output(err);
                if (results.affectedRows !== 1) return res.output(new Error("用户注册失败！"), 400);

                // 记录日志
                log.add("sysLog", "注册用户", username);

                // 确保返回格式与Android应用期望的一致
                res.output(null, 200, {
                    username: username,
                    status: "0",
                    message: "注册成功"
                });
            });
        });
    } catch (error) {
        // 捕获任何未处理的异常
        console.error("注册过程中发生错误:", error);
        return res.output(new Error("注册过程中发生错误: " + error.message), 500);
    }
};

// 登录处理函数
exports.userLogin = (req, res) => {
    try {
        // 验证必要的字段
        const { username, password } = req.body;

        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400);
        }

        const sql = "select * from users where username=?";
        db.query(sql, username, (err, results) => {
            if (err) return res.output(err);
            if (results.length !== 1) return res.output(new Error("登录失败"), 400);
            if (password !== results[0].password) {
                return res.output(new Error("密码错误，登录失败！"), 400);
            }

            // 创建用户对象，移除密码
            const user = { ...results[0], password: "" };

            // 生成JWT令牌
            const token = jwt.sign(user, config.jwtSecretKey, {
                expiresIn: config.expiresIn,
            });

            // 获取用户角色
            const role = results[0].role || "user";

            // 记录日志
            log.add("sysLog", "登录系统", username);

            // 返回成功信息
            res.output(null, 200, {
                token: token,
                username: username,
                role: role
            });
        });
    } catch (error) {
        // 捕获任何未处理的异常
        console.error("登录过程中发生错误:", error);
        return res.output(new Error("登录过程中发生错误: " + error.message), 500);
    }
};

// 注册并上传图像处理函数
exports.registerWithAvatar = (req, res) => {
    try {
        // 记录请求信息，方便调试
        console.log("收到注册请求，请求头:", req.headers);
        console.log("请求方法:", req.method);
        console.log("Content-Type:", req.headers['content-type']);

        // 检查Content-Type是否为multipart/form-data
        const contentType = req.headers['content-type'] || '';
        if (contentType.includes('multipart/form-data')) {
            console.log("检测到multipart/form-data请求，将使用multer处理");

            // 使用imageManager中的createUploader创建上传器
            const uploader = createUploader(IMAGE_TYPES.AVATAR, 1);

            // 使用uploader处理文件上传
            uploader.single('avatar')(req, res, async function (err) {
                if (err) {
                    return res.output(err, 400, null, false);
                }

                if (!req.file) {
                    return res.output(new Error('请选择要上传的头像'), 400, null, false);
                }

                // 继续处理注册逻辑
                await processRegistration(req, res);
            });
        } else {
            // 如果不是multipart/form-data，直接处理注册逻辑
            processRegistration(req, res);
        }
    } catch (error) {
        console.error("处理注册请求时发生错误:", error);
        return res.output(new Error("处理注册请求时发生错误: " + error.message), 500, null, false);
    }
};

// 处理注册逻辑
async function processRegistration(req, res) {
    try {
        // 获取请求数据
        let username, password, phone, name, gender, birthday, idCard;
        let avatarFile = null;

        // 处理不同类型的请求
        if (req.file && req.body.registerRequest) {
            // 处理带文件的multipart/form-data请求
            console.log("处理带文件的multipart/form-data请求");

            // 获取上传的文件
            avatarFile = req.file;
            console.log("上传的文件:", avatarFile);

            // 解析registerRequest字段
            try {
                const registerData = typeof req.body.registerRequest === 'string'
                    ? JSON.parse(req.body.registerRequest)
                    : req.body.registerRequest;

                console.log("解析的registerRequest:", registerData);

                username = registerData.username;
                password = registerData.password;
                phone = registerData.phone || '';
                name = registerData.name || '';
                gender = registerData.gender || '';
                birthday = registerData.birthday || '';
                idCard = registerData.idCard || '';
            } catch (error) {
                console.error("解析registerRequest失败:", error.message);
                return res.output(new Error("解析registerRequest失败: " + error.message), 400, null, false);
            }
        } else {
            // 尝试从JSON请求体中获取数据
            console.log("尝试从JSON请求体中获取数据");

            if (typeof req.body === 'object' && req.body !== null) {
                username = req.body.username;
                password = req.body.password;
                phone = req.body.phone || '';
                name = req.body.name || '';
                gender = req.body.gender || '';
                birthday = req.body.birthday || '';
                idCard = req.body.idCard || '';
            }
        }

        // 验证必要的字段
        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400, null, false);
        }

        console.log("处理的注册数据:", { username, password, phone, name, gender, birthday, idCard });

        // 设置默认值
        let role = "user";
        let avatarUrl = "";

        // 检查用户名是否已存在
        const sql = "select username from users where username=?";
        db.query(sql, username, async (err, results) => {
            if (err) {
                return res.output(err, 500, null, false);
            }

            if (results.length === 1) {
                return res.output(new Error("用户名被占用"), 400, null, false);
            }

            try {
                // 如果有上传文件，处理文件上传
                if (avatarFile) {
                    const { savedImages } = await handleFileUpload([avatarFile], IMAGE_TYPES.AVATAR, {
                        businessType: 'user',
                        businessId: null, // 将在用户创建后更新
                        uploadUserId: null,
                        uploadUserName: username
                    });

                    if (savedImages && savedImages.length > 0) {
                        avatarUrl = savedImages[0].url;
                    }
                }

                // 插入用户信息
                const sqlStr = "insert into users set ?";
                db.query(sqlStr, { username, password, phone, name, gender, birthday, idCard, role, avatarUrl }, async (err, results) => {
                    if (err) {
                        return res.output(err, 500, null, false);
                    }

                    if (results.affectedRows !== 1) {
                        return res.output(new Error("用户注册失败！"), 400, null, false);
                    }

                    // 记录日志
                    log.add("sysLog", "注册用户并上传头像", username);

                    // 返回成功信息
                    res.output(null, 200, {
                        username: username,
                        status: "0",
                        avatarPath: avatarUrl,
                        message: "注册成功"
                    }, true);
                });
            } catch (error) {
                console.error("处理文件上传时发生错误:", error);
                return res.output(new Error("处理文件上传时发生错误: " + error.message), 500, null, false);
            }
        });
    } catch (error) {
        console.error("处理注册逻辑时发生错误:", error);
        return res.output(new Error("处理注册逻辑时发生错误: " + error.message), 500, null, false);
    }
}

exports.checkUsername = (req, res) => {
    const username = req.query.username;
    const sql = 'select * from users where username= ?'
    db.query(sql, username, (err, results) => {
        if (err) return res.output(err)
        if (results.length !== 1) return res.output(new Error("获取用户信息失败！"), 400)
        // 移除密码字段
        const userInfo = { ...results[0] };
        delete userInfo.password;
        res.output(null, 200, userInfo);
    })
}
