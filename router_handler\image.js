const path = require('path');
const fs = require('fs');
const db = require('../db/index');
const config = require('../config');
const log = require('./log')
const {
  IMAGE_TYPES,
  IMAGE_PATHS,
  createUploader,
  handleFileUpload,  
  getImageById,
  getImagesByBusiness,
  deleteImage,
  ensureDirectoryExists
} = require('../utils/imageManager');

// 根据业务类型获取图片类型
const getImageType = (businessType) => {
  switch (businessType) {
    case 'user':
      return IMAGE_TYPES.AVATAR;
    case 'product':
      return IMAGE_TYPES.PRODUCT;
    case 'follow':
      return IMAGE_TYPES.FOLLOW;
    case 'customer':
      return IMAGE_TYPES.CUSTOMER;
    case 'component':
      return IMAGE_TYPES.COMPONENT;
    default:
      return IMAGE_TYPES.AVATAR;
  }
};

// 验证上传参数
const validateUploadParams = (businessType, businessId, operator, res) => {
  // 验证必需参数
  if (!businessType) {
    res.output(new Error('businessType is required'), 400, null, false);
    return false;
  }
  if (!businessId) {
    res.output(new Error('businessId is required'), 400, null, false);
    return false;
  }
  if (!operator) {
    res.output(new Error('operator is required'), 400, null, false);
    return false;
  }
  // 验证业务类型
  if (!config.businessTypes.includes(businessType)) {
    res.output(new Error(`businessType must be one of: ${config.businessTypes.join(', ')}`), 400, null, false);
    return false;
  }
  return true;
};

// 处理文件移动到正确目录
const moveFilesToCorrectDirectory = (files, imageType) => {
  if (imageType === IMAGE_TYPES.AVATAR) {
    return; // 已经在正确目录，无需移动
  }
  const filesToProcess = Array.isArray(files) ? files : [files];
  for (const file of filesToProcess) {
    const oldPath = file.path;
    const newPath = path.join(IMAGE_PATHS[imageType], file.filename);
    // 确保目标目录存在
    ensureDirectoryExists(IMAGE_PATHS[imageType]);
    // 移动文件
    fs.renameSync(oldPath, newPath);
    file.path = newPath;
  }
};

// 核心上传处理逻辑
const processImageUpload = async (files, businessType, businessId, operator, req) => {
  // 根据业务类型确定图片类型
  const imageType = getImageType(businessType);
  // 移动文件到正确目录
  moveFilesToCorrectDirectory(files, imageType);
  // 处理文件上传
  const savedImages = await handleFileUpload(files, imageType, {
    businessType: businessType,
    businessId: businessId,
    uploadUserId: req.user ? req.user.id : null,
    uploadUserName: operator
  });
  return { savedImages, imageType };
};

// 通用图片上传处理器
const uploadImage = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.AVATAR, 1);

  uploader.single('image')(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.file) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    const { businessType, businessId, operator } = req.body;

    // 验证参数
    if (!validateUploadParams(businessType, businessId, operator, res)) {
      return; // 验证失败，响应已发送
    }

    try {
      // 处理图片上传
      const { savedImages } = await processImageUpload([req.file], businessType, businessId, operator, req);

      // 更新业务数据中的图片字段
      const imageUrl = savedImages[0].url;
      await updateBusinessImageField(businessType, businessId, imageUrl);

      res.output(null, 200, imageUrl, true);
    } catch (error) {
      console.error('通用图片上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 批量图片上传处理器
const uploadMultipleImages = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.AVATAR, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.files || req.files.length === 0) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    const { businessType, businessId, operator } = req.body;

    // 验证参数
    if (!validateUploadParams(businessType, businessId, operator, res)) {
      return; // 验证失败，响应已发送
    }

    try {
      // 处理图片上传
      const { savedImages } = await processImageUpload(req.files, businessType, businessId, operator, req);

      // 返回图片URL列表
      const imageUrls = savedImages.map(img => img.url);

      res.output(null, 200, imageUrls, true);
    } catch (error) {
      console.error('批量图片上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 获取图片列表
const getImageList = async (req, res) => {
  try {
    const { businessType, businessId, page = 1, size = 10 } = req.query;

    let images = [];

    if (businessType && businessId) {
      // 验证业务类型
      if (!config.businessTypes.includes(businessType)) {
        return res.output(new Error(`businessType must be one of: ${config.businessTypes.join(', ')}`), 400, null, false);
      }

      images = await getImagesByBusiness(businessType, businessId);
    } else {
      // 如果没有指定业务类型和ID，返回空列表
      images = [];
    }

    // 简单的分页处理
    const startIndex = (page - 1) * size;
    const endIndex = startIndex + parseInt(size);
    const paginatedImages = images.slice(startIndex, endIndex);

    // 转换图片数据格式，返回包含id和filePath的对象列表
    const formattedImages = paginatedImages.map(img => {
      let filePath = '';

      // 优先使用file_path字段
      if (img.file_path) {
        // 将Windows路径分隔符转换为URL路径分隔符
        filePath = img.file_path.replace(/\\/g, '/');
      }
      // 备用方案：使用image_type和file_name构建路径
      else if (img.image_type && img.file_name) {
        filePath = `images/${img.image_type}/${img.file_name}`;
      }
      // 最后的备用方案
      else if (img.fileName) {
        filePath = `images/${businessType}/${img.fileName}`;
      }

      return {
        id: img.id,
        filePath: filePath
      };
    });

    res.output(null, 200, formattedImages, true);
  } catch (error) {
    console.error('获取图片列表失败:', error);
    res.output(error, 500, null, false);
  }
};

// 删除图片
const deleteImageHandler = async (req, res) => {
  try {
    const { imageId } = req.body;

    const imageInfo = await getImageById(imageId);

    if (!imageInfo) {
      return res.output(new Error('图片不存在'), 404, null, false);
    }

    await deleteImage(imageId);

    res.output(null, 200, { message: '图片删除成功' }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 批量删除图片
const batchDeleteImages = async (req, res) => {
  try {
    const { imageIds } = req.body;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.output(new Error('请提供要删除的图片ID列表'), 400, null, false);
    }

    const results = [];

    for (const imageId of imageIds) {
      try {
        await deleteImage(imageId);
        results.push({ imageId, success: true });
      } catch (error) {
        results.push({ imageId, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;

    res.output(null, 200, {
      message: `批量删除完成，成功删除 ${successCount}/${imageIds.length} 张图片`,
      results: results
    }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 通过URL删除图片处理器
const deleteImageByUrl = async (req, res) => {
  try {
    const { businessType, businessId, operator, imageId } = req.query;

    // 验证必需参数
    if (!businessType || !businessId || !operator || !imageId) {
      return res.output(new Error('businessType, businessId, operator, and imageId are required'), 400, null, false);
    }

    // 验证业务类型
    if (!config.businessTypes.includes(businessType)) {
      return res.output(new Error(`businessType must be one of: ${config.businessTypes.join(', ')}`), 400, null, false);
    }

    // 根据imageIdl查找图片记录
    const images = await getImagesByBusiness(businessType, businessId);
    const imageToDelete = images.find(img => img.id === imageId);

    if (!imageToDelete) {
      return res.output(new Error('图片不存在'), 404, null, false);
    }

    // 删除图片记录
    await deleteImage(imageToDelete.id);

    res.output(null, 200, { message: '图片删除成功' }, true);
  } catch (error) {
    console.error('删除图片失败:', error);
    res.output(error, 500, null, false);
  }
};

// 获取业务对象的所有图片
const getBusinessImages = async (req, res) => {
  try {
    const { businessType, businessId } = req.query;

    // 验证必需参数
    if (!businessType || !businessId) {
      return res.output(new Error('businessType and businessId are required'), 400, null, false);
    }

    // 验证业务类型
    if (!config.businessTypes.includes(businessType)) {
      return res.output(new Error(`businessType must be one of: ${config.businessTypes.join(', ')}`), 400, null, false);
    }

    // 获取图片列表
    const images = await getImagesByBusiness(businessType, businessId);

    // 转换图片数据格式，返回包含id和filePath的对象列表
    const formattedImages = images.map(img => {
      let filePath = '';

      // 优先使用file_path字段
      if (img.file_path) {
        // 将Windows路径分隔符转换为URL路径分隔符
        filePath = img.file_path.replace(/\\/g, '/');
      }
      // 备用方案：使用image_type和file_name构建路径
      else if (img.image_type && img.file_name) {
        filePath = `images/${img.image_type}/${img.file_name}`;
      }
      // 最后的备用方案
      else if (img.fileName) {
        filePath = `images/${businessType}/${img.fileName}`;
      }

      return {
        id: img.id,
        filePath: filePath
      };
    });

    res.output(null, 200, formattedImages, true);
  } catch (error) {
    console.error('获取业务图片失败:', error);
    res.output(error, 500, null, false);
  }
};

// 更新业务数据中的图片字段
async function updateBusinessImageField(businessType, businessId, imageUrl) {
  return new Promise((resolve, reject) => {
    let sql;
    let tableName;
    let imageField;

    switch (businessType) {
      case 'user':
        tableName = 'users';
        imageField = 'avatarUrl';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      case 'customer':
        tableName = 'customers';
        imageField = 'image';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      default:
        // 对于其他业务类型，不更新数据库字段，只保存图片记录
        return resolve();
    }

    db.query(sql, [imageUrl, businessId], (err, results) => {
      if (err) {
        console.error(`更新${businessType}图片字段失败:`, err);
        return reject(err);
      }

      if (results.affectedRows === 0) {
        console.warn(`未找到要更新的${businessType}记录: ${businessId}`);
      }

      resolve(results);
    });
  });
}

module.exports = {
  uploadImage,
  uploadMultipleImages,  

  updateBusinessImageField,

  getImageList,
  getBusinessImages,
  deleteImage: deleteImageHandler,
  deleteImageByUrl,
  batchDeleteImages
};
