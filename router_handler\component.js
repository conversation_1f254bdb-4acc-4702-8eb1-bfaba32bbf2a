const db = require('../db/index')
const log = require('./log')

const getComponentList = (req, res) => {
  try {
    const user = req.user;

    // 获取查询参数
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const keyword = req.query.keyword || '';

    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`code` LIKE ? OR `name` LIKE ? OR `model` LIKE ? OR `standard` LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM components WHERE delFlag = 0  " + extSql;

    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT	* FROM components "
        + "WHERE delFlag = 0 "
        + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];

      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }     

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        });
      });
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const getComponentDetail = (req, res) => {
  try {
    const user = req.user;
    const id = req.query.id;
    const sql = "select * from components where id=?";
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取部件信息失败！"), 400)
      // 处理部件信息
      const componentInfo = { ...results[0] };     
      res.output(null, 200, componentInfo);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const addComponent = (req, res) => {
  try {
    const user = req.user;
    const { code, name, model, standard, remark } = req.body;
    const sqlStr = "insert into components set ?";
    db.query(sqlStr, { code, name, model, standard, remark, delFlag: 0 }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("添加部件失败！"), 400);
      res.output(null, 200, { id: results.insertId, message: "添加部件成功" }, true);
    });
    log.add("sysLog", "添加部件:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const updateComponent = (req, res) => {
  try {
    const user = req.user;
    const { id, code, name, model, standard, remark } = req.body;
    const sqlStr = 'update components set code=?, name=?, model=?, standard=?, remark=? where id =?'
    db.query(sqlStr, [code, name, model, standard, remark, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新部件信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code }, true)
    })
    log.add("sysLog", "更新部件信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const deleteComponent = (req, res) => {
  try {
    const user = req.user;
    const id = req.body.id;
    const sqlStr = "update components set delFlag=1 where id=?";
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("删除部件失败！"), 400);
      res.output(null, 200, { message: "删除部件成功" }, true);
    });
    log.add("sysLog", "删除部件:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getComponentList,
  getComponentDetail,
  addComponent,
  updateComponent,
  deleteComponent
};
