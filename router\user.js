const express = require('express')
const router = express.Router()
const user_handler = require('../router_handler/user')

const expressJoi = require('@escook/express-joi')
const { check_user_schema, update_user_schema, update_user_Profile_schema,update_password_schema,get_user_all_schema,get_user_name_schema,pend_user_schema,get_user_id_schema} = require('../schema/user')

// 获取用户的基本信息
router.get('/profile', user_handler.getUserInfo)
router.post('/updateProfile', expressJoi(update_user_Profile_schema), user_handler.updateProfile)
router.post('/changePassword', expressJoi(update_password_schema), user_handler.updatePassword)

//用户管理
router.get('/all',expressJoi(get_user_all_schema),user_handler.getUserList)
router.get('/findByName',expressJoi(get_user_name_schema)  ,user_handler.getByName)
router.post('/updateUser', express<PERSON>oi(update_user_schema), user_handler.updateUser)
router.post('/delete', user_handler.deleteUser)

// 审核用户
router.get('/pendingList', user_handler.getPendingList);
router.get('/pendingUser', expressJoi(pend_user_schema), user_handler.getPendingUser);
router.post('/auditUser', expressJoi(check_user_schema), user_handler.auditUser)
router.get('/detail',expressJoi(get_user_id_schema)  ,user_handler.getUserDetail)

// 将路由导出
module.exports = router