const db = require('../db/index')
const log = require('./log')

// 查询
const getUserInfo = (req, res) => {
    try {
        // 从 JWT token 中获取用户信息
        const user = req.user;
        if (!user || !user.username) {
            return res.output(new Error("未登录或登录已过期"), 401);
        }
        getUserInfoByName(user.username, user.username, res);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const getByName = (req, res) => {
    try {
        // 从 JWT token 中获取用户信息
        const user = req.user;
        if (!user || !user.username) {
            return res.output(new Error("未登录或登录已过期"), 401);
        }
        const name = req.query.username;
        getUserInfoByName(name, user.username, res);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

function getUserInfoByName(searchName, operateName, res) {
    try {
        const sql = 'select * from users where username= ?'
        db.query(sql, searchName, (err, results) => {
            if (err) return res.output(err)
            if (results.length !== 1) return res.output(new Error("获取用户信息失败！"), 400)
            // 移除密码字段
            const userInfo = { ...results[0] };
            delete userInfo.password;            
            res.output(null, 200, userInfo);
        })
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

function getUserInfoById(searchId, operateName, res) {
    try {
        const sql = 'select * from users where id= ?'
        db.query(sql, searchId, (err, results) => {
            if (err) return res.output(err)
            if (results.length !== 1) return res.output(new Error("获取用户信息失败！"), 400)
            // 移除密码字段
            const userInfo = { ...results[0] };
            delete userInfo.password;            
            res.output(null, 200, userInfo);
        })
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const getUserDetail = (req, res) => {
    try {
        // 从 JWT token 中获取用户信息
        const user = req.user;
        if (!user || !user.username) {
            return res.output(new Error("未登录或登录已过期"), 401);
        }
        const id = req.query.id;
        getUserInfoById(id, user.username, res);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

// 修改基础信息
const updateProfile = (req, res) => {
    // 从 JWT token 中获取用户信息
    const user = req.user;
    let { name, gender, birthday, idCard, phone } = req.body
    const sqlStr = 'update users set name=?,gender=?,birthday=?,idCard=?,phone=?,modifyUserName=? where username =?'
    db.query(sqlStr, [name, gender, birthday, idCard, phone, user.username, user.username], (err, results) => {
        if (err) return res.output(err)
        if (results.affectedRows !== 1) return res.output(new Error('更新用户信息失败！'), 400)
        res.output(null, 200, { username: user.username, name: name, gender: gender, birthday: birthday, idCard: idCard, phone: phone }, true)
    })
    log.add("sysLog", "修改用户信息:" + user.username, user.username);
}

// 修改审核信息
const updateUser = (req, res) => {
    const user = req.user;
    let { username, name, gender, birthday, idCard, phone, employeeId, department, position, job, permissionTemplate, role, remark } = req.body;
    const sqlStr = 'update users set name=?,gender=?,birthday=?,idCard=?,phone=?,employeeId=?,department=?,position=?,job=?,permissionTemplate=?,role=?,remark=?,modifyUserName=? where username =?'
    db.query(sqlStr, [name, gender, birthday, idCard, phone, employeeId, department, position, job, permissionTemplate, role, remark, user.username, username], (err, results) => {
        if (err) return res.output(err)
        if (results.affectedRows !== 1) return res.output(new Error('更新用户信息失败！'), 400)
        res.output(null, 200, { username: user.username, name: name, gender: gender, birthday: birthday, idCard: idCard, phone: phone }, true)
    })
    log.add("sysLog", "修改用户信息:" + username, user.username);
}

// 修改密码
const updatePassword = (req, res) => {
    // 从 JWT token 中获取用户信息
    const user = req.user;
    if (!user || !user.username) {
        return res.output(new Error("未登录或登录已过期"), 401);
    }

    const { oldPassword, newPassword } = req.body;
    const sql = 'select * from users where username=?';
    db.query(sql, user.username, (err, results) => {
        if (err) return res.output(err);
        if (results.length !== 1) return res.output(new Error("用户不存在"), 400);
        if (oldPassword != results[0].password) return res.output(new Error("原密码错误"), 400);
        const sql = 'update users set password=? where username=?';
        db.query(sql, [newPassword, user.username], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("修改密码失败"), 400);
            res.output(null, 200, { message: "修改密码成功" }, true);
        });
    });
    log.add("sysLog", "修改密码:" + user.username, user.username);
}

const deleteUser = (req, res) => {
    const user = req.user;
    const username = req.body.username;
    const sql = "update users set delFlag=1 where  username != 'admin' AND username=?";
    db.query(sql, username, (err, results) => {
        if (err) return res.output(err);
        if (results.affectedRows !== 1) return res.output(new Error("删除用户失败"), 400);
        res.output(null, 200, { message: "删除用户成功" }, true);
    })
    log.add("sysLog", "删除用户:" + username, user.username);
}

const getUserList = (req, res) => {
    try {
        const user = req.user;

        // 获取查询参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const keyword = req.query.keyword || '';

        console.log("查询参数:", { page, size, keyword });

        // 构建SQL查询
        let extSql = "";
        let params = [];

        if (keyword && keyword.trim() !== '') {
            extSql = "AND (us.`username` LIKE ? OR us.`name` LIKE ? OR us.`employeeId` LIKE ?) ";
            const searchTerm = `%${keyword}%`;
            params = [searchTerm, searchTerm, searchTerm];
        }

        // 计算分页
        const offset = (page - 1) * size;

        // 查询总数
        const countSql = "SELECT COUNT(*) as total FROM users us WHERE us.username != 'admin' AND us.delFlag = 0 AND us.`status` = 1 " + extSql;

        db.query(countSql, params, (countErr, countResults) => {
            if (countErr) {
                console.error("查询总数出错:", countErr);
                return res.output(countErr, 500);
            }

            const total = countResults[0].total;

            // 查询数据
            const dataSql = "SELECT	us.id, us.username, us.name, us.employeeId, us.avatarUrl, us.role, us.phone, us.department, us.position, us.job, "
                + "dp.`name` AS departmentName, ps.`name` AS positionName, jb.`name` as jobName FROM users us "
                + "LEFT JOIN departments dp ON (us.department = dp.`code` and dp.`status`=1 and dp.delFlag=0) "
                + "LEFT JOIN positions ps ON (us.position = ps.`code` and ps.delFlag=0) "
                + "LEFT JOIN posts jb ON (us.job = jb.`code` and jb.delFlag=0) "
                + "WHERE us.username != 'admin' AND us.delFlag = 0 	AND us.`status` = 1 "
                + extSql + " LIMIT ?, ?";
            const dataParams = [...params, offset, size];

            db.query(dataSql, dataParams, (err, results) => {
                if (err) {
                    console.error("查询数据出错:", err);
                    return res.output(err, 500);
                }

                // 处理结果，移除敏感信息并处理头像URL
                const safeResults = results.map(userItem => {
                    // 移除密码字段
                    const { password, ...safeUser } = userItem;    
                    return safeUser;
                });

                // 返回结果
                res.output(null, 200, {
                    total: total,
                    page: page,
                    size: size,
                    list: safeResults
                });
            });
        });
    } catch (error) {
        console.error("查询用户列表出错:", error);
        res.output(new Error("查询用户列表出错: " + error.message), 500);
    }
}

// 审核用户
const getPendingList = (req, res) => {
    try {
        const user = req.user;
        const dataSql = "SELECT * FROM users WHERE username != 'admin' and delFlag=0 ORDER BY registerTime DESC;";
        db.query(dataSql, null, (err, results) => {
            if (err) {
                console.error("查询数据出错:", err);
                return res.output(err, 500);
            }
            // 处理结果，移除敏感信息并处理头像URL
            const safeResults = results.map(userItem => {
                // 移除密码字段
                const { password, ...safeUser } = userItem;    
                return safeUser;
            });
            // 返回结果
            res.output(null, 200, safeResults, true);
        });
    } catch (error) {
        res.output(new Error("查询用户审核列表出错: " + error.message), 500, null, false);
    }
}

const getPendingUser = (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;
        const dataSql = "SELECT * FROM users WHERE id=?;";
        db.query(dataSql, id, (err, results) => {
            if (err) {
                console.error("查询数据出错:", err);
                return res.output(err, 500);
            }
            // 移除密码字段
            const userInfo = { ...results[0] };
            delete userInfo.password;            
            // 返回结果
            res.output(null, 200, userInfo, true);
        });
    } catch (error) {
        res.output(new Error("查询用户审核信息出错: " + error.message), 500, null, false);
    }
}

const auditUser = (req, res) => {
    try {
        const user = req.user;
        const { id, employeeId, department, position, job, permissionTemplate, role, status, remark } = req.body;
        // 构建SQL查询
        let extSql = "";
        let params = [status, remark, user.username, id];
        if (status == "1") {
            extSql = "role=?, employeeId=?, department=?, position=?, job=?, permissionTemplate=?, "
            params = [role, employeeId, department, position, job, permissionTemplate, status, remark, user.username, id]
        }
        const sqlStr = "update users set "
            + extSql
            + "status=?, remark=?, modifyUserName=? where id =?"
        db.query(sqlStr, params, (err, results) => {
            if (err) return res.output(err)
            if (results.affectedRows !== 1) return res.output(new Error('更新用户信息失败！'), 400)
            res.output(null, 200, { id: id, employeeId: employeeId, department: department, position: position, job: job, permissionTemplate: permissionTemplate, role: role, status: status, remark: remark }, true)
        })
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

module.exports = {
    getUserInfo,
    getByName,   
    updateProfile,

    updateUser,
    updatePassword,

    getUserList,
    deleteUser,

    getPendingList,
    getPendingUser,
    getUserDetail,
    auditUser
}